<?php
/**
 * Test script to verify email URL plugin functionality
 */

use Magento\Framework\App\Bootstrap;

require __DIR__ . '/app/bootstrap.php';

$bootstrap = Bootstrap::create(BP, $_SERVER);
$objectManager = $bootstrap->getObjectManager();

// Get the email template filter
$emailFilter = $objectManager->get(\Magento\Email\Model\Template\Filter::class);

// Test URL directive processing (this should trigger our plugin)
echo "Testing email template URL directive processing...\n";

// Test email template content with URL directive
$emailContent = 'Welcome! Please <a href="{{url path="customer/account/login"}}">login here</a> to continue.';

echo "Before email template processing:\n";
echo "Email content: $emailContent\n";

// This should trigger our afterUrlDirective plugin when processing the {{url}} directive
$result = $emailFilter->filter($emailContent);
echo "After email template processing:\n";
echo "Result: $result\n";

echo "\nTest completed. Check logs for plugin activity.\n";
